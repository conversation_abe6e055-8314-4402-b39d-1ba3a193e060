'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FileUpload } from "@/components/ui/file-upload";
import { FeatureInput } from "@/components/ui/feature-input";
import { useCategories } from "@/hooks/useCategories";
import { useBrands } from "@/hooks/useBrands";
import { createProductWithValidation as createProduct } from "@/utils/supabase/product-helpers";
import { uploadMultipleFilesClient as uploadMultipleFiles } from "@/utils/supabase/storage-client";
import { createClient } from "@/utils/supabase/client";
import { redirect, useRouter } from "next/navigation";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";

export default function NewProductPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [urlImages, setUrlImages] = useState<string[]>([]);
  const [features, setFeatures] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Fetch categories and brands using React Query
  const { data: categories = [], isLoading: categoriesLoading } = useCategories({
    include_inactive: false
  });
  const { data: brands = [], isLoading: brandsLoading } = useBrands({
    include_inactive: false
  });

  // Form data
  const [formData, setFormData] = useState({
    name: '',
    price: '',
    description: '',
    category_id: '',
    brand_id: '',
    stock: '10'
  });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle file selection
  const handleFilesSelected = (files: File[]) => {
    setUploadedFiles(prev => [...prev, ...files]);
  };

  // Handle URL addition
  const handleUrlsAdded = (urls: string[]) => {
    setUrlImages(prev => [...prev, ...urls]);
  };

  // Handle file removal
  const handleFileRemoved = (index: number) => {
    setUploadedFiles(prev => {
      const newFiles = [...prev];
      newFiles.splice(index, 1);
      return newFiles;
    });
  };
  
  // Load user data and set default selections
  useEffect(() => {
    async function loadUser() {
      try {
        const supabase = createClient();
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          router.push("/sign-in?redirect=/admin/products/new");
          return;
        }

        setUser(user);
      } catch (error) {
        console.error('Error loading user:', error);
        setError('Failed to load user data');
      }
    }

    loadUser();
  }, [router]);

  // Set default category and brand when data loads
  useEffect(() => {
    if (categories.length > 0 && !formData.category_id) {
      setFormData(prev => ({ ...prev, category_id: categories[0].id }));
    }
  }, [categories, formData.category_id]);

  useEffect(() => {
    if (brands.length > 0 && !formData.brand_id) {
      setFormData(prev => ({ ...prev, brand_id: brands[0].id }));
    }
  }, [brands, formData.brand_id]);
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const { name, price, description, category_id, brand_id, stock } = formData;

      // Validate required fields
      if (!name || !price || !category_id || !stock) {
        throw new Error('Please fill in all required fields');
      }

      // Determine status based on stock
      const stockNum = parseInt(stock);
      let status = "In Stock";
      if (stockNum === 0) {
        status = "Out of Stock";
      } else if (stockNum < 10) {
        status = "Low Stock";
      }

      // Upload images if any
      let imageUrls: string[] = [];
      if (uploadedFiles.length > 0) {
        imageUrls = await uploadMultipleFiles(uploadedFiles);
      }

      // Combine uploaded images and URL images
      const allImages = [...imageUrls, ...urlImages];
      const imageUrl = allImages[0] || '';

      // Find category name for backward compatibility
      const selectedCategory = categories.find(cat => cat.id === category_id);
      const categoryName = selectedCategory?.name || '';

      // Create product with all data using the validation helper
      await createProduct({
        name,
        price: parseFloat(price),
        description,
        image: imageUrl, // Only use the first image as the main image
        category: categoryName, // Keep for backward compatibility
        category_id, // New foreign key
        brand_id: brand_id || undefined, // Optional brand
        stock: stockNum,
        status
      });

      toast({
        title: "Success",
        description: "Product created successfully",
      });

      // Redirect to products page on success
      router.push("/admin/products");
    } catch (error: any) {
      console.error('Error creating product:', error);
      setError(error.message || 'Failed to create product');
      toast({
        title: "Error",
        description: error.message || "Failed to create product",
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Link href="/admin/products">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold tracking-tight">Add New Product</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Product Details</CardTitle>
          <CardDescription>
            Enter the details for the new product.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="bg-destructive/15 text-destructive p-3 rounded-md mb-6">
              {error}
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Product Name</Label>
                <Input 
                  id="name" 
                  name="name" 
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Pro Tour Racket" 
                  required 
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="price">Price (R)</Label>
                <Input 
                  id="price" 
                  name="price" 
                  type="number" 
                  step="0.01" 
                  value={formData.price}
                  onChange={handleInputChange}
                  placeholder="1999.99" 
                  required 
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="category_id">Category *</Label>
                {categoriesLoading ? (
                  <div className="h-10 bg-muted animate-pulse rounded-md neo-shadow-inset" />
                ) : categories.length > 0 ? (
                  <Select
                    name="category_id"
                    value={formData.category_id}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, category_id: value }))}
                  >
                    <SelectTrigger className="min-h-[44px] neo-shadow-inset">
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent className="neo-shadow">
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                          {category.parent && (
                            <span className="text-muted-foreground ml-2">
                              ({category.parent.name})
                            </span>
                          )}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="text-sm text-muted-foreground p-3 border rounded-md neo-shadow-inset">
                    No categories available. Please create categories first.
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="brand_id">Brand</Label>
                {brandsLoading ? (
                  <div className="h-10 bg-muted animate-pulse rounded-md neo-shadow-inset" />
                ) : brands.length > 0 ? (
                  <Select
                    name="brand_id"
                    value={formData.brand_id}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, brand_id: value }))}
                  >
                    <SelectTrigger className="min-h-[44px] neo-shadow-inset">
                      <SelectValue placeholder="Select a brand (optional)" />
                    </SelectTrigger>
                    <SelectContent className="neo-shadow">
                      <SelectItem value="">No Brand</SelectItem>
                      {brands.map((brand) => (
                        <SelectItem key={brand.id} value={brand.id}>
                          {brand.name}
                          {brand.country && (
                            <span className="text-muted-foreground ml-2">
                              ({brand.country})
                            </span>
                          )}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="text-sm text-muted-foreground p-3 border rounded-md neo-shadow-inset">
                    No brands available. You can create brands later.
                  </div>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="stock">Stock Quantity</Label>
                <Input 
                  id="stock" 
                  name="stock" 
                  type="number" 
                  value={formData.stock}
                  onChange={handleInputChange}
                  placeholder="10" 
                  required 
                />
              </div>
              
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="description">Description</Label>
                <Textarea 
                  id="description" 
                  name="description" 
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Enter product description..." 
                  rows={5}
                />
              </div>
              
              <div className="space-y-2 md:col-span-2">
                <Label>Product Images</Label>
                <FileUpload
                  onFilesSelected={handleFilesSelected}
                  onUrlsAdded={handleUrlsAdded}
                  onFileRemoved={handleFileRemoved}
                  maxFiles={5}
                  label="Upload product images"
                  allowUrls={true}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Upload up to 5 images. The first image will be used as the main product image.
                </p>
              </div>
              
              <div className="space-y-2 md:col-span-2">
                <Label>Product Features</Label>
                <FeatureInput 
                  features={features}
                  onChange={setFeatures}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Add key features of the product to highlight its selling points.
                </p>
              </div>
            </div>
            
            <div className="flex justify-end gap-4">
              <Link href="/admin/products">
                <Button type="button" variant="outline">Cancel</Button>
              </Link>
              <Button 
                type="submit" 
                disabled={isLoading}
                className={isLoading ? 'opacity-70' : ''}
              >
                {isLoading ? 'Creating...' : 'Create Product'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
