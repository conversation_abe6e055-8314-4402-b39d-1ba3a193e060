import { NextResponse } from 'next/server';
import { createClient } from '../../../utils/supabase/server';
import { createServiceClient } from '../../../utils/supabase/service';
import { z } from 'zod';

// Validation schema for brand data
const brandSchema = z.object({
  name: z.string().min(1, 'Brand name is required').max(100, 'Brand name too long'),
  slug: z.string().optional(),
  description: z.string().optional(),
  logo: z.string().url().optional().or(z.literal('')),
  website: z.string().url().optional().or(z.literal('')),
  country: z.string().max(100).optional(),
  founded_year: z.number().int().min(1800).max(new Date().getFullYear()).optional(),
  is_active: z.boolean().optional(),
  metadata: z.record(z.any()).optional()
});

// Helper function to generate slug from name
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

// Helper function to check admin authentication
async function checkAdminAuth(supabase: any, user: any) {
  if (!user) {
    return { error: 'Unauthorized', status: 401 };
  }

  const { data: userData } = await supabase
    .from('users')
    .select('role')
    .eq('id', user.id)
    .single();

  if (!userData || userData.role !== 'admin') {
    return { error: 'Forbidden - Admin access required', status: 403 };
  }

  return null;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const includeInactive = searchParams.get('include_inactive') === 'true';
    const search = searchParams.get('search');
    const country = searchParams.get('country');
    
    // Use service client for public brand data
    const supabase = createServiceClient();

    let query = supabase
      .from('brands')
      .select(`
        id,
        name,
        slug,
        description,
        logo,
        website,
        country,
        founded_year,
        is_active,
        product_count,
        metadata,
        created_at,
        updated_at
      `);

    // Filter by active status unless specifically requesting inactive
    if (!includeInactive) {
      query = query.eq('is_active', true);
    }

    // Filter by country
    if (country) {
      query = query.eq('country', country);
    }

    // Search functionality
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    // Order by name
    query = query.order('name', { ascending: true });

    const { data: brands, error } = await query;

    if (error) {
      console.error('Error fetching brands:', error);
      return NextResponse.json(
        { error: 'Failed to fetch brands' },
        { status: 500 }
      );
    }

    return NextResponse.json(brands);
  } catch (error: any) {
    console.error('Error in brands API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    // Check admin authentication
    const authError = await checkAdminAuth(supabase, user);
    if (authError) {
      return NextResponse.json(
        { error: authError.error },
        { status: authError.status }
      );
    }

    const body = await request.json();
    
    // Validate input data
    const validationResult = brandSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const { name, description, logo, website, country, founded_year, is_active, metadata } = validationResult.data;

    // Generate slug if not provided
    const slug = validationResult.data.slug || generateSlug(name);

    // Check if slug already exists
    const { data: existingBrand } = await supabase
      .from('brands')
      .select('id')
      .eq('slug', slug)
      .single();

    if (existingBrand) {
      return NextResponse.json(
        { error: 'Brand with this name already exists' },
        { status: 409 }
      );
    }

    // Use service role client for database operations
    const serviceSupabase = createServiceClient();
    const { data: brand, error } = await serviceSupabase
      .from('brands')
      .insert({
        name,
        slug,
        description,
        logo,
        website,
        country,
        founded_year,
        is_active: is_active !== undefined ? is_active : true,
        metadata: metadata || {},
        product_count: 0
      })
      .select(`
        id,
        name,
        slug,
        description,
        logo,
        website,
        country,
        founded_year,
        is_active,
        product_count,
        metadata,
        created_at,
        updated_at
      `)
      .single();

    if (error) {
      console.error('Error creating brand:', error);
      return NextResponse.json(
        { error: 'Failed to create brand' },
        { status: 500 }
      );
    }

    return NextResponse.json(brand, { status: 201 });
  } catch (error: any) {
    console.error('Error in brands POST API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
