import { NextResponse } from 'next/server';
import { createClient } from '../../../utils/supabase/server';
import { createServiceClient } from '../../../utils/supabase/service';
import { z } from 'zod';

// Validation schema for brand data
const brandSchema = z.object({
  name: z.string().min(1, 'Brand name is required').max(100, 'Brand name too long'),
  slug: z.string().optional(),
  description: z.string().optional(),
  logo: z.string().url().optional().or(z.literal('')),
  website: z.string().url().optional().or(z.literal('')),
  country: z.string().max(100).optional(),
  founded_year: z.number().int().min(1800).max(new Date().getFullYear()).optional(),
  is_active: z.boolean().optional(),
  metadata: z.record(z.any()).optional()
});

// Helper function to generate slug from name
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

// Helper function to check admin authentication
async function checkAdminAuth(supabase: any, user: any) {
  if (!user) {
    return { error: 'Unauthorized', status: 401 };
  }

  const { data: userData } = await supabase
    .from('users')
    .select('role')
    .eq('id', user.id)
    .single();

  if (!userData || userData.role !== 'admin') {
    return { error: 'Forbidden - Admin access required', status: 403 };
  }

  return null;
}

export async function GET(request: Request) {
  try {
    // Use service client for public brand data
    const supabase = createServiceClient();

    // Check if brands table exists by trying a simple query
    try {
      const { data: brands, error } = await supabase
        .from('brands')
        .select('id')
        .limit(1);

      if (error && error.code === '42P01') {
        // Table doesn't exist, return empty array
        console.log('Brands table does not exist yet. Please run the database migration.');
        return NextResponse.json([]);
      }

      if (error) {
        console.error('Error checking brands table:', error);
        return NextResponse.json([]);
      }

      // If we got here, table exists, return empty array for now
      // This will be updated once the migration is run
      return NextResponse.json([]);
    } catch (error: any) {
      console.log('Brands table does not exist yet. Please run the database migration.');
      return NextResponse.json([]);
    }
  } catch (error: any) {
    console.error('Error in brands API:', error);
    return NextResponse.json([]);
  }
}

export async function POST(request: Request) {
  try {
    return NextResponse.json(
      {
        error: 'Brand creation is temporarily disabled. Please run the database migration first.',
        migration_required: true,
        migration_file: 'DATABASE_MIGRATION_CATEGORIES_BRANDS.sql'
      },
      { status: 503 }
    );
  } catch (error: any) {
    console.error('Error in brands POST API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
