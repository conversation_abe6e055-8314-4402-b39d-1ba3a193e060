import { NextResponse } from 'next/server';
import { createClient } from '../../../utils/supabase/server';
import { createServiceClient } from '../../../utils/supabase/service';
import { z } from 'zod';

// Validation schema for category data
const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required').max(100, 'Category name too long'),
  slug: z.string().optional(),
  description: z.string().optional(),
  image: z.string().url().optional().or(z.literal('')),
  parent_id: z.string().uuid().optional().nullable(),
  sort_order: z.number().int().min(0).optional(),
  is_active: z.boolean().optional(),
  metadata: z.record(z.any()).optional()
});

// Helper function to generate slug from name
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

// Helper function to check admin authentication
async function checkAdminAuth(supabase: any, user: any) {
  if (!user) {
    return { error: 'Unauthorized', status: 401 };
  }

  const { data: userData } = await supabase
    .from('users')
    .select('role')
    .eq('id', user.id)
    .single();

  if (!userData || userData.role !== 'admin') {
    return { error: 'Forbidden - Admin access required', status: 403 };
  }

  return null;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');

    // Use service client for public category data
    const supabase = createServiceClient();

    // Use the current simple schema that exists
    let query = supabase
      .from('categories')
      .select('id, name, image, count, created_at, updated_at');

    // Search functionality for current schema
    if (search) {
      query = query.ilike('name', `%${search}%`);
    }

    // Order by name
    query = query.order('name', { ascending: true });

    const { data: categories, error } = await query;

    if (error) {
      console.error('Error fetching categories:', error);
      return NextResponse.json(
        { error: 'Failed to fetch categories' },
        { status: 500 }
      );
    }

    // Transform current schema data to match expected format
    const transformedCategories = (categories || []).map(cat => ({
      id: cat.id,
      name: cat.name,
      slug: cat.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
      description: null,
      image: cat.image,
      parent_id: null,
      sort_order: 0,
      is_active: true,
      product_count: cat.count || 0,
      metadata: {},
      created_at: cat.created_at,
      updated_at: cat.updated_at,
      parent: null
    }));

    return NextResponse.json(transformedCategories);
  } catch (error: any) {
    console.error('Error in categories API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    return NextResponse.json(
      {
        error: 'Category creation is temporarily disabled. Please run the database migration first.',
        migration_required: true,
        migration_file: 'DATABASE_MIGRATION_CATEGORIES_BRANDS.sql'
      },
      { status: 503 }
    );
  } catch (error: any) {
    console.error('Error in categories POST API:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
